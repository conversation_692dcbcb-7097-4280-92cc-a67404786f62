package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmChecklistImportacao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ChecklistImportacaoRNA rn = null;

    public FrmChecklistImportacao() {
        try {
            rn = (freedom.bytecode.rn.ChecklistImportacaoRNA) getRN(freedom.bytecode.rn.wizard.ChecklistImportacaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbChecklist();
        init_tbConcessionariaTipo();
        init_vboxPrincipal();
        init_vboxForm();
        init_vBoxlblMontadoraPrincipal();
        init_vBoxlblMontadoraEspacamento();
        init_vBoxlblMontadora();
        init_lblMontadora();
        init_lblDescricaoMontadora();
        init_btnOlaMundo();
        init_gpBoxChecklists();
        init_gridChecklist();
        init_FrmChecklistImportacao();
    }

    public MOB_CHECKLIST tbChecklist;

    private void init_tbChecklist() {
        tbChecklist = rn.tbChecklist;
        tbChecklist.setName("tbChecklist");
        tbChecklist.setMaxRowCount(200);
        tbChecklist.setWKey("342012;34201");
        tbChecklist.setRatioBatchSize(20);
        getTables().put(tbChecklist, "tbChecklist");
        tbChecklist.applyProperties();
    }

    public CONCESSIONARIA_TIPO tbConcessionariaTipo;

    private void init_tbConcessionariaTipo() {
        tbConcessionariaTipo = rn.tbConcessionariaTipo;
        tbConcessionariaTipo.setName("tbConcessionariaTipo");
        tbConcessionariaTipo.setMaxRowCount(200);
        tbConcessionariaTipo.setWKey("342012;34202");
        tbConcessionariaTipo.setRatioBatchSize(20);
        getTables().put(tbConcessionariaTipo, "tbConcessionariaTipo");
        tbConcessionariaTipo.applyProperties();
    }

    protected TFForm FrmChecklistImportacao = this;
    private void init_FrmChecklistImportacao() {
        FrmChecklistImportacao.setName("FrmChecklistImportacao");
        FrmChecklistImportacao.setCaption("Importar checklist");
        FrmChecklistImportacao.setClientHeight(464);
        FrmChecklistImportacao.setClientWidth(498);
        FrmChecklistImportacao.setColor("clBtnFace");
        FrmChecklistImportacao.setWOrigem("EhMain");
        FrmChecklistImportacao.setWKey("342012");
        FrmChecklistImportacao.setSpacing(0);
        FrmChecklistImportacao.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(498);
        vboxPrincipal.setHeight(464);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(0);
        vboxPrincipal.setPaddingLeft(0);
        vboxPrincipal.setPaddingRight(0);
        vboxPrincipal.setPaddingBottom(0);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmChecklistImportacao.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFVBox vboxForm = new TFVBox();

    private void init_vboxForm() {
        vboxForm.setName("vboxForm");
        vboxForm.setLeft(0);
        vboxForm.setTop(0);
        vboxForm.setWidth(496);
        vboxForm.setHeight(439);
        vboxForm.setBorderStyle("stNone");
        vboxForm.setPaddingTop(0);
        vboxForm.setPaddingLeft(0);
        vboxForm.setPaddingRight(0);
        vboxForm.setPaddingBottom(0);
        vboxForm.setMarginTop(0);
        vboxForm.setMarginLeft(0);
        vboxForm.setMarginRight(0);
        vboxForm.setMarginBottom(0);
        vboxForm.setSpacing(1);
        vboxForm.setFlexVflex("ftTrue");
        vboxForm.setFlexHflex("ftTrue");
        vboxForm.setScrollable(false);
        vboxForm.setBoxShadowConfigHorizontalLength(10);
        vboxForm.setBoxShadowConfigVerticalLength(10);
        vboxForm.setBoxShadowConfigBlurRadius(5);
        vboxForm.setBoxShadowConfigSpreadRadius(0);
        vboxForm.setBoxShadowConfigShadowColor("clBlack");
        vboxForm.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(vboxForm);
        vboxForm.applyProperties();
    }

    public TFHBox vBoxlblMontadoraPrincipal = new TFHBox();

    private void init_vBoxlblMontadoraPrincipal() {
        vBoxlblMontadoraPrincipal.setName("vBoxlblMontadoraPrincipal");
        vBoxlblMontadoraPrincipal.setLeft(0);
        vBoxlblMontadoraPrincipal.setTop(0);
        vBoxlblMontadoraPrincipal.setWidth(487);
        vBoxlblMontadoraPrincipal.setHeight(64);
        vBoxlblMontadoraPrincipal.setBorderStyle("stNone");
        vBoxlblMontadoraPrincipal.setPaddingTop(5);
        vBoxlblMontadoraPrincipal.setPaddingLeft(0);
        vBoxlblMontadoraPrincipal.setPaddingRight(0);
        vBoxlblMontadoraPrincipal.setPaddingBottom(0);
        vBoxlblMontadoraPrincipal.setMarginTop(0);
        vBoxlblMontadoraPrincipal.setMarginLeft(0);
        vBoxlblMontadoraPrincipal.setMarginRight(0);
        vBoxlblMontadoraPrincipal.setMarginBottom(0);
        vBoxlblMontadoraPrincipal.setSpacing(5);
        vBoxlblMontadoraPrincipal.setFlexVflex("ftFalse");
        vBoxlblMontadoraPrincipal.setFlexHflex("ftTrue");
        vBoxlblMontadoraPrincipal.setScrollable(false);
        vBoxlblMontadoraPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxlblMontadoraPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxlblMontadoraPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxlblMontadoraPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxlblMontadoraPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblMontadoraPrincipal.setBoxShadowConfigOpacity(75);
        vBoxlblMontadoraPrincipal.setVAlign("tvTop");
        vboxForm.addChildren(vBoxlblMontadoraPrincipal);
        vBoxlblMontadoraPrincipal.applyProperties();
    }

    public TFVBox vBoxlblMontadoraEspacamento = new TFVBox();

    private void init_vBoxlblMontadoraEspacamento() {
        vBoxlblMontadoraEspacamento.setName("vBoxlblMontadoraEspacamento");
        vBoxlblMontadoraEspacamento.setLeft(0);
        vBoxlblMontadoraEspacamento.setTop(0);
        vBoxlblMontadoraEspacamento.setWidth(17);
        vBoxlblMontadoraEspacamento.setHeight(41);
        vBoxlblMontadoraEspacamento.setBorderStyle("stNone");
        vBoxlblMontadoraEspacamento.setPaddingTop(0);
        vBoxlblMontadoraEspacamento.setPaddingLeft(0);
        vBoxlblMontadoraEspacamento.setPaddingRight(0);
        vBoxlblMontadoraEspacamento.setPaddingBottom(0);
        vBoxlblMontadoraEspacamento.setMarginTop(0);
        vBoxlblMontadoraEspacamento.setMarginLeft(0);
        vBoxlblMontadoraEspacamento.setMarginRight(0);
        vBoxlblMontadoraEspacamento.setMarginBottom(0);
        vBoxlblMontadoraEspacamento.setSpacing(1);
        vBoxlblMontadoraEspacamento.setFlexVflex("ftFalse");
        vBoxlblMontadoraEspacamento.setFlexHflex("ftFalse");
        vBoxlblMontadoraEspacamento.setScrollable(false);
        vBoxlblMontadoraEspacamento.setBoxShadowConfigHorizontalLength(10);
        vBoxlblMontadoraEspacamento.setBoxShadowConfigVerticalLength(10);
        vBoxlblMontadoraEspacamento.setBoxShadowConfigBlurRadius(5);
        vBoxlblMontadoraEspacamento.setBoxShadowConfigSpreadRadius(0);
        vBoxlblMontadoraEspacamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblMontadoraEspacamento.setBoxShadowConfigOpacity(75);
        vBoxlblMontadoraPrincipal.addChildren(vBoxlblMontadoraEspacamento);
        vBoxlblMontadoraEspacamento.applyProperties();
    }

    public TFVBox vBoxlblMontadora = new TFVBox();

    private void init_vBoxlblMontadora() {
        vBoxlblMontadora.setName("vBoxlblMontadora");
        vBoxlblMontadora.setLeft(17);
        vBoxlblMontadora.setTop(0);
        vBoxlblMontadora.setWidth(217);
        vBoxlblMontadora.setHeight(41);
        vBoxlblMontadora.setBorderStyle("stNone");
        vBoxlblMontadora.setPaddingTop(0);
        vBoxlblMontadora.setPaddingLeft(0);
        vBoxlblMontadora.setPaddingRight(0);
        vBoxlblMontadora.setPaddingBottom(0);
        vBoxlblMontadora.setMarginTop(0);
        vBoxlblMontadora.setMarginLeft(0);
        vBoxlblMontadora.setMarginRight(0);
        vBoxlblMontadora.setMarginBottom(0);
        vBoxlblMontadora.setSpacing(1);
        vBoxlblMontadora.setFlexVflex("ftFalse");
        vBoxlblMontadora.setFlexHflex("ftTrue");
        vBoxlblMontadora.setScrollable(false);
        vBoxlblMontadora.setBoxShadowConfigHorizontalLength(10);
        vBoxlblMontadora.setBoxShadowConfigVerticalLength(10);
        vBoxlblMontadora.setBoxShadowConfigBlurRadius(5);
        vBoxlblMontadora.setBoxShadowConfigSpreadRadius(0);
        vBoxlblMontadora.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblMontadora.setBoxShadowConfigOpacity(75);
        vBoxlblMontadoraPrincipal.addChildren(vBoxlblMontadora);
        vBoxlblMontadora.applyProperties();
    }

    public TFLabel lblMontadora = new TFLabel();

    private void init_lblMontadora() {
        lblMontadora.setName("lblMontadora");
        lblMontadora.setLeft(0);
        lblMontadora.setTop(0);
        lblMontadora.setWidth(71);
        lblMontadora.setHeight(16);
        lblMontadora.setCaption("Montadora");
        lblMontadora.setFontColor("clBlack");
        lblMontadora.setFontSize(-13);
        lblMontadora.setFontName("Tahoma");
        lblMontadora.setFontStyle("[fsBold]");
        lblMontadora.setVerticalAlignment("taVerticalCenter");
        lblMontadora.setWordBreak(false);
        vBoxlblMontadora.addChildren(lblMontadora);
        lblMontadora.applyProperties();
    }

    public TFLabel lblDescricaoMontadora = new TFLabel();

    private void init_lblDescricaoMontadora() {
        lblDescricaoMontadora.setName("lblDescricaoMontadora");
        lblDescricaoMontadora.setLeft(0);
        lblDescricaoMontadora.setTop(17);
        lblDescricaoMontadora.setWidth(96);
        lblDescricaoMontadora.setHeight(16);
        lblDescricaoMontadora.setCaption("concession\u00E1ria");
        lblDescricaoMontadora.setFontColor("clRed");
        lblDescricaoMontadora.setFontSize(-13);
        lblDescricaoMontadora.setFontName("Tahoma");
        lblDescricaoMontadora.setFontStyle("[fsBold]");
        lblDescricaoMontadora.setFieldName("DESCRICAO");
        lblDescricaoMontadora.setTable(tbConcessionariaTipo);
        lblDescricaoMontadora.setVerticalAlignment("taVerticalCenter");
        lblDescricaoMontadora.setWordBreak(false);
        vBoxlblMontadora.addChildren(lblDescricaoMontadora);
        lblDescricaoMontadora.applyProperties();
    }

    public TFButton btnOlaMundo = new TFButton();

    private void init_btnOlaMundo() {
        btnOlaMundo.setName("btnOlaMundo");
        btnOlaMundo.setUploadMime("image/*");
        btnOlaMundo.setLeft(234);
        btnOlaMundo.setTop(0);
        btnOlaMundo.setWidth(120);
        btnOlaMundo.setHeight(35);
        btnOlaMundo.setCaption("Ola Mundo!");
        btnOlaMundo.setFontColor("clWindowText");
        btnOlaMundo.setFontSize(-11);
        btnOlaMundo.setFontName("Tahoma");
        btnOlaMundo.setFontStyle("[]");
        btnOlaMundo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnOlaMundoClick(event);
            processarFlow("FrmChecklistImportacao", "btnOlaMundo", "OnClick");
        });
        btnOlaMundo.setImageId(7000105);
        btnOlaMundo.setColor("clBtnFace");
        btnOlaMundo.setAccess(false);
        btnOlaMundo.setIconReverseDirection(false);        vBoxlblMontadoraPrincipal.addChildren(btnOlaMundo);
        btnOlaMundo.applyProperties();
    }

    public TFGroupbox gpBoxChecklists = new TFGroupbox();

    private void init_gpBoxChecklists() {
        gpBoxChecklists.setName("gpBoxChecklists");
        gpBoxChecklists.setLeft(0);
        gpBoxChecklists.setTop(65);
        gpBoxChecklists.setWidth(489);
        gpBoxChecklists.setHeight(437);
        gpBoxChecklists.setCaption("Checklists Dispon\u00EDveis");
        gpBoxChecklists.setFontColor("clWindowText");
        gpBoxChecklists.setFontSize(-11);
        gpBoxChecklists.setFontName("Tahoma");
        gpBoxChecklists.setFontStyle("[]");
        gpBoxChecklists.setFlexVflex("ftTrue");
        gpBoxChecklists.setFlexHflex("ftTrue");
        gpBoxChecklists.setScrollable(false);
        gpBoxChecklists.setClosable(false);
        gpBoxChecklists.setClosed(false);
        gpBoxChecklists.setOrient("coHorizontal");
        gpBoxChecklists.setStyle("grp3D");
        gpBoxChecklists.setHeaderImageId(0);
        vboxForm.addChildren(gpBoxChecklists);
        gpBoxChecklists.applyProperties();
    }

    public TFGrid gridChecklist = new TFGrid();

    private void init_gridChecklist() {
        gridChecklist.setName("gridChecklist");
        gridChecklist.setLeft(2);
        gridChecklist.setTop(15);
        gridChecklist.setWidth(485);
        gridChecklist.setHeight(420);
        gridChecklist.setAlign("alClient");
        gridChecklist.setTable(tbChecklist);
        gridChecklist.setFlexVflex("ftTrue");
        gridChecklist.setFlexHflex("ftTrue");
        gridChecklist.setPagingEnabled(true);
        gridChecklist.setFrozenColumns(0);
        gridChecklist.setShowFooter(false);
        gridChecklist.setShowHeader(true);
        gridChecklist.setMultiSelection(false);
        gridChecklist.setGroupingEnabled(false);
        gridChecklist.setGroupingExpanded(false);
        gridChecklist.setGroupingShowFooter(false);
        gridChecklist.setCrosstabEnabled(false);
        gridChecklist.setCrosstabGroupType("cgtConcat");
        gridChecklist.setEditionEnabled(true);
        gridChecklist.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(332);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridChecklist.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("ATIVO");
        item1.setTitleCaption("Ativo");
        item1.setWidth(51);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("ATIVO = 'S'");
        item2.setEvalType("etExpression");
        item2.setImageId(7000105);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridChecklistCheckClick(event);
            processarFlow("FrmChecklistImportacao", "item2", "OnClick");
        });
        item1.getImages().add(item2);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("((ATIVO = 'N') OR (ATIVO IS NULL))");
        item3.setEvalType("etExpression");
        item3.setImageId(7000106);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridChecklistUnCheckClick(event);
            processarFlow("FrmChecklistImportacao", "item3", "OnClick");
        });
        item1.getImages().add(item3);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(false);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridChecklist.getColumns().add(item1);
        gpBoxChecklists.addChildren(gridChecklist);
        gridChecklist.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnOlaMundoClick(final Event<Object> event) {
        if (btnOlaMundo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOlaMundo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridChecklistCheckClick(final Event<Object> event);

    public abstract void gridChecklistUnCheckClick(final Event<Object> event);

}