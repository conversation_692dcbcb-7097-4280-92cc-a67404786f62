package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.ChecklistImportacaoRNW;
public class ChecklistImportacaoRNA extends ChecklistImportacaoRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void filtrarCheckList(Double idMontadora) throws DataException {
        tbChecklist.close();
        tbChecklist.setFilterMONTADORA(idMontadora);
        tbChecklist.setOrderBy("ID_CHECKLIST");
        tbChecklist.open();
    }

    public void filtrarConcessionariaTipo(Double idMontadora) throws DataException {
        tbConcessionariaTipo.close();
        tbConcessionariaTipo.setFilterCOD_TIPO_CONCESSIONARIA(idMontadora);
        tbConcessionariaTipo.open();
    }

    public void checklistSetAtivo(boolean b) {

    }

    public double tbChecklistGetId() {
        return tbChecklist.getID_CHECKLIST().asDecimal();
    }

    public void recarregarRegistro() throws DataException {
        tbChecklist.refreshRecord();
    }
}
