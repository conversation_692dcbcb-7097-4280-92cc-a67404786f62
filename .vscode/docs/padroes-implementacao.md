# Padrões de Implementação Freedom

## Estrutura de Eventos

### Grid Events
```java
@Override
public void gridNomeCheckClick(Event<Object> event) {
    // Implementação para check
}

@Override
public void gridNomeUnCheckClick(Event<Object> event) {
    // Implementação para uncheck
}
```

### Button Events
```java
@Override
public void btnNomeClick(final Event event) {
    // Implementação do botão
}
```

## Padrões de Regras de Negócio

### Filtros de Tabela
```java
public void filtrarTabela(Double parametro) throws DataException {
    tbNome.close();
    tbNome.setFilterCAMPO(parametro);
    tbNome.setOrderBy("CAMPO_ORDEM");
    tbNome.open();
}
```

### Tratamento de Exceções
```java
try {
    // Lógica de negócio
} catch (DataException e) {
    CrmServiceUtil.showError("Mensagem de erro", e);
}
```

## Estrutura de Cursores

### Inicialização
```java
private void init_tbNome() {
    tbNome = rn.tbNome;
    tbNome.setName("tbNome");
    tbNome.setMaxRowCount(200);
    tbNome.setWKey("codigo;subcod");
    getTables().put(tbNome, "tbNome");
    tbNome.applyProperties();
}
```