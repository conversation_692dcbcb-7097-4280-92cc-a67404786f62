# Workflow para Agentes IA - Telas Freedom

## Processo de Criação/Edição de Telas

### 1. Edição do Arquivo .frm
- **Responsabilidade**: Agente IA edita diretamente o arquivo `.frm`
- **Localização**: `design/NomeTela.frm`
- **Conteúdo**: Definição de componentes, layout, propriedades visuais

### 2. Geração Manual (Desenvolvedor)
- **Ação**: Desenvolvedor executa geração no Freedom IDE
- **Resultado**: Atualiza arquivo `wizard/FrmNomeTela.java`
- **Status**: Aguardar confirmação do desenvolvedor

### 3. Implementação de Comportamentos
- **Arquivo**: `FrmNomeTelaA.java`
- **Responsabilidade**: Agente IA implementa eventos e comportamentos
- **Herança**: Extends do arquivo wizard correspondente

### 4. Regras de Negócio
- **Arquivo**: `NomeTelaRNA.java`
- **Responsabilidade**: Agente IA implementa lógica de negócio
- **Funcionalidades**: Filtros, validações, manipulação de cursores

## Padrões de Nomenclatura

### Estrutura de Arquivos
```
design/FrmNomeTela.frm                           # Design visual
src/main/java/freedom/bytecode/form/wizard/
  ??? FrmNomeTela.java                          # Wizard (gerado)
src/main/java/freedom/bytecode/form/
  ??? FrmNomeTelaA.java                         # Implementação
src/main/java/freedom/bytecode/rn/
  ??? NomeTelaRNA.java                          # Regras de negócio
```

### Convenções de Código

#### Arquivo Wizard (Gerado)
- Classe abstrata
- Inicialização de componentes (`init_nomeComponente()`)
- Métodos abstratos para eventos
- Não editar manualmente

#### Arquivo de Implementação (A.java)
- Extends da classe wizard
- Implementa métodos abstratos
- Eventos concretos (`@Override`)
- Lógica de interface

#### Arquivo de Regras (RNA.java)
- Extends da classe wizard RN correspondente
- Métodos de filtro (`filtrarTabela()`)
- Validações de negócio
- Manipulação de cursores

## Restrições para Agentes IA

### ? Não Permitido
- Criar novos cursores (apenas usar existentes)
- Editar arquivos wizard diretamente
- Modificar estrutura de herança

### ? Permitido
- Editar arquivos .frm
- Implementar comportamentos em A.java
- Criar regras de negócio em RNA.java
- Usar cursores existentes
- Solicitar criação de cursores específicos

## Exemplo Prático

### Solicitação: "Criar tela de importação de checklist"

1. **Editar**: `design/FrmChecklistImportacao.frm`
2. **Aguardar**: Geração manual do wizard
3. **Implementar**: `FrmChecklistImportacaoA.java`
4. **Criar**: `ChecklistImportacaoRNA.java`

### Componentes Típicos
- `TFGrid` - Grids de dados
- `TFVBox/TFHBox` - Containers
- `TFButton` - Botões
- `TFTable` - Cursores/tabelas